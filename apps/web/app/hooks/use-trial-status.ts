'use client';

import { useMemo } from 'react';
import { useQuery } from '@rocicorp/zero/react';
import { useZero } from './use-zero';

// TypeScript interfaces for trial system
export interface TrialInfo {
  accountId: string;
  status: TrialStatus;
  startedAt: number | null;
  endsAt: number | null;
  daysRemaining: number | null;
  isExpired: boolean;
  isActive: boolean;
  isConverted: boolean;
  isInactive: boolean;
  isPaid: boolean;
  progressPercentage: number;
  timeRemaining: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

// Trial status enum type matching database enum
export type TrialStatus = 'inactive' | 'active' | 'expired' | 'converted';

export interface UseTrialStatusOptions {
  accountId: string;
  enabled?: boolean;
}

export interface UseTrialStatusReturn {
  trialInfo: TrialInfo | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

/**
 * Hook to get real-time trial status for an account
 * @param options - Configuration options
 * @returns Trial status information with real-time updates
 */
export function useTrialStatus({ 
  accountId, 
  enabled = true 
}: UseTrialStatusOptions): UseTrialStatusReturn {
  const zero = useZero();

  // Query account data with trial fields using Zero
  const accountQuery = zero.query.accounts
    .where('id', '=', accountId)
    .one();

  const [account, accountDetail] = useQuery(accountQuery, { enabled });

  // Calculate comprehensive trial information
  const trialInfo = useMemo((): TrialInfo | null => {
    if (!account) return null;

    const now = Date.now();
    const status = (account.trial_status as TrialStatus) || 'inactive';
    const startedAt = account.trial_started_at || null;
    const endsAt = account.trial_ends_at || null;

    // Calculate time-based properties
    let daysRemaining: number | null = null;
    let isExpired = false;
    let progressPercentage = 0;
    let timeRemaining: TrialInfo['timeRemaining'] = null;

    if (endsAt && startedAt) {
      const totalDuration = endsAt - startedAt;
      const elapsed = now - startedAt;
      const remaining = endsAt - now;

      // Calculate days remaining (can be negative if expired)
      daysRemaining = Math.ceil(remaining / (1000 * 60 * 60 * 24));
      isExpired = remaining <= 0;

      // Calculate progress percentage (0-100)
      progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

      // Calculate detailed time remaining
      if (remaining > 0) {
        const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
        const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));

        timeRemaining = { days, hours, minutes };
      }
    }

    // Override isExpired if status is already expired
    if (status === 'expired') {
      isExpired = true;
    }

    // Status flags for easy conditional rendering
    const isConverted = status === 'converted';
    const isInactive = status === 'inactive';
    const isPaid = isConverted; // Add paid status flag

    // Determine final status - override if expired
    const finalStatus = isExpired && status === 'active' ? 'expired' : status;

    return {
      accountId,
      status: finalStatus,
      startedAt,
      endsAt,
      daysRemaining,
      isExpired,
      isActive: finalStatus === 'active' && !isExpired,
      isConverted,
      isPaid, // Add to return object
      isInactive,
      progressPercentage,
      timeRemaining,
    };
  }, [account, accountId]);

  // Auto-update expired trials
  useMemo(() => {
    if (trialInfo?.isExpired && trialInfo.status === 'active') {
      // Trigger mutation to update status to expired
      zero.mutate.accounts.updateTrialStatus({
        accountId,
        status: 'expired',
      });
    }
  }, [trialInfo?.isExpired, trialInfo?.status, zero.mutate.accounts, accountId]);

  return {
    trialInfo,
    isLoading: accountDetail.type === 'unknown',
    error: null, // Zero doesn't have error states in the result type
    refetch: () => {
      // Zero handles real-time updates automatically, but we can force a refresh if needed
      // This is mainly for error recovery scenarios
    },
  };
}

/**
 * Hook to get trial status for multiple accounts
 * @param accountIds - Array of account IDs
 * @returns Map of account ID to trial info
 */
export function useMultipleTrialStatuses(accountIds: string[]) {
  const zero = useZero();

  const accountsQuery = zero.query.accounts
    .where('id', '=', accountIds[0] || ''); // Zero doesn't support 'in' operator, using first ID for now

  const [accounts, accountsDetail] = useQuery(accountsQuery);

  const trialInfoMap = useMemo(() => {
    const map = new Map<string, TrialInfo>();
    
    accounts.forEach(account => {
      const now = Date.now();
      const status = (account.trial_status as TrialStatus) || 'inactive';
      const startedAt = account.trial_started_at || null;
      const endsAt = account.trial_ends_at || null;

      let daysRemaining: number | null = null;
      let isExpired = false;
      let progressPercentage = 0;
      let timeRemaining: TrialInfo['timeRemaining'] = null;

      if (endsAt && startedAt) {
        const totalDuration = endsAt - startedAt;
        const elapsed = now - startedAt;
        const remaining = endsAt - now;

        daysRemaining = Math.ceil(remaining / (1000 * 60 * 60 * 24));
        isExpired = remaining <= 0;
        progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

        if (remaining > 0) {
          const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
          const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
          
          timeRemaining = { days, hours, minutes };
        }
      }

      const isActive = status === 'active' && !isExpired;
      const isConverted = status === 'converted';
      const isInactive = status === 'inactive';

      map.set(account.id, {
        accountId: account.id,
        status: isExpired && status === 'active' ? 'expired' : status,
        startedAt,
        endsAt,
        daysRemaining,
        isExpired,
        isActive,
        isConverted,
        isInactive,
        progressPercentage,
        timeRemaining,
      });
    });

    return map;
  }, [accounts]);

  return {
    trialInfoMap,
    isLoading: accountsDetail.type === 'unknown',
    error: null, // Zero doesn't have error states in the result type
  };
}
