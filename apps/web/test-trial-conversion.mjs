#!/usr/bin/env node

/**
 * Test script to verify trial conversion functionality
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const client = createClient(supabaseUrl, supabaseKey);

async function testTrialConversion() {
  console.log('🧪 Testing trial conversion functionality...\n');

  try {
    // 1. Create a test account with active trial
    console.log('1. Creating test account with active trial...');
    const testAccountId = crypto.randomUUID();
    const testUserId = crypto.randomUUID();
    
    const { data: account, error: accountError } = await client
      .from('accounts')
      .insert({
        id: testAccountId,
        primary_owner_user_id: testUserId,
        name: 'Test Trial Account',
        trial_status: 'active',
        trial_started_at: new Date().toISOString(),
        trial_ends_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
      })
      .select()
      .single();

    if (accountError) {
      console.error('❌ Failed to create test account:', accountError);
      return;
    }
    console.log('✅ Test account created:', account.id);

    // 2. Create a test order
    console.log('\n2. Creating test order...');
    const testOrderId = 'test_order_' + Date.now();
    
    const { data: order, error: orderError } = await client
      .from('orders')
      .insert({
        id: testOrderId,
        account_id: testAccountId,
        billing_customer_id: 1, // Assuming there's at least one billing customer
        status: 'pending',
        billing_provider: 'stripe',
        total_amount: 29.99,
        currency: 'USD'
      })
      .select()
      .single();

    if (orderError) {
      console.error('❌ Failed to create test order:', orderError);
      return;
    }
    console.log('✅ Test order created:', order.id);

    // 3. Simulate payment success - convert trial to paid
    console.log('\n3. Simulating payment success and trial conversion...');
    
    // First, get the order to verify account_id
    const { data: orderData } = await client
      .from('orders')
      .select('account_id')
      .eq('id', testOrderId)
      .single();

    if (orderData?.account_id) {
      const { error: trialError } = await client
        .from('accounts')
        .update({ 
          trial_status: 'converted'
        })
        .eq('id', orderData.account_id)
        .in('trial_status', ['active', 'expired']);

      if (trialError) {
        console.error('❌ Failed to convert trial status:', trialError);
        return;
      }
      console.log('✅ Trial converted to paid status for account:', orderData.account_id);
    }

    // 4. Verify the conversion
    console.log('\n4. Verifying trial conversion...');
    const { data: updatedAccount, error: verifyError } = await client
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId)
      .single();

    if (verifyError) {
      console.error('❌ Failed to verify account:', verifyError);
      return;
    }

    if (updatedAccount.trial_status === 'converted') {
      console.log('✅ Trial status successfully converted to "converted"');
    } else {
      console.log('❌ Trial status not converted. Current status:', updatedAccount.trial_status);
    }

    // 5. Clean up test data
    console.log('\n5. Cleaning up test data...');
    await client.from('orders').delete().eq('id', testOrderId);
    await client.from('accounts').delete().eq('id', testAccountId);
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 Trial conversion test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testTrialConversion();
